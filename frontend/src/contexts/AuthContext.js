/**
 * Authentication Context - Manages user authentication state
 * Provides centralized authentication management for the entire application
 * Ensures all components have access to authentication state and methods
 */

import React, { createContext, useContext, useState, useEffect } from 'react';
import { notification } from 'antd';

const AuthContext = createContext();

// Auto-login service URL (for Google OAuth)
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3000';

export const useAuth = () => {
  const context = useContext(AuthContext);
  if (!context) {
    throw new Error('useAuth must be used within an AuthProvider');
  }
  return context;
};

export const AuthProvider = ({ children }) => {
  const [user, setUser] = useState(null);
  const [isAuthenticated, setIsAuthenticated] = useState(false);
  const [loading, setLoading] = useState(true);
  const [hasValidPackage, setHasValidPackage] = useState(false);
  const [authError, setAuthError] = useState(null);

  // Check authentication status on app load and set up periodic checks
  useEffect(() => {
    // Check if user is already authenticated from localStorage first
    const storedUser = localStorage.getItem('user');
    const storedAuth = localStorage.getItem('isAuthenticated');

    if (storedUser && storedAuth === 'true') {
      console.log('🔍 [AUTH] Found stored authentication, restoring user session...');
      try {
        const userData = JSON.parse(storedUser);
        setUser(userData);
        setIsAuthenticated(true);
        setHasValidPackage(true); // Assume valid for OAuth users
        setLoading(false);
        return; // Skip server check if we have stored auth
      } catch (error) {
        console.error('❌ [AUTH] Failed to parse stored user data:', error);
      }
    }

    // Only check server if no stored auth found
    checkAuthStatus();

    // Set up periodic session check every 5 minutes
    const sessionCheckInterval = setInterval(() => {
      if (isAuthenticated) {
        console.log('🔍 [AUTH] Performing periodic session check...');
        checkAuthStatus();
      }
    }, 5 * 60 * 1000); // 5 minutes

    return () => {
      clearInterval(sessionCheckInterval);
    };
  }, [isAuthenticated]);

  const checkAuthStatus = async () => {
    try {
      setLoading(true);
      setAuthError(null);

      console.log('🔍 [AUTH] Checking authentication status with auto-login service...');

      // Check if user is authenticated via cookies
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/verify`, {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const userData = await response.json();
        console.log('✅ [AUTH] User authenticated:', userData);

        setUser(userData);
        setIsAuthenticated(true);
        localStorage.setItem('user', JSON.stringify(userData));
        localStorage.setItem('isAuthenticated', 'true');

        // Check package access
        await checkPackageAccess(userData.id);
      } else {
        console.log('❌ [AUTH] User not authenticated');
        clearAuthData();
      }
    } catch (error) {
      console.error('❌ [AUTH] Authentication check failed:', error);
      setAuthError('Failed to verify authentication status');
      clearAuthData();
    } finally {
      setLoading(false);
    }
  };

  const checkPackageAccess = async (userId) => {
    try {
      const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/users/${userId}/package-access`, {
        method: 'GET',
        credentials: 'include',
      });

      if (response.ok) {
        const data = await response.json();
        setHasValidPackage(data.has_bot_insta_access);
      } else {
        setHasValidPackage(false);
      }
    } catch (error) {
      console.error('Package access check failed:', error);
      setHasValidPackage(false);
    }
  };

  const login = async (userData, skipPackageCheck = false) => {
    console.log('🔍 [AUTH] Login called with userData:', userData);
    setUser(userData);
    setIsAuthenticated(true);
    localStorage.setItem('user', JSON.stringify(userData));
    localStorage.setItem('isAuthenticated', 'true');

    // Skip package access check for OAuth login since cookies aren't shared
    if (!skipPackageCheck) {
      await checkPackageAccess(userData.id);
    } else {
      console.log('🔍 [AUTH] Skipping package access check for OAuth login');
      // For OAuth users, assume they have valid package access
      setHasValidPackage(true);
    }
  };

  const logout = async () => {
    try {
      console.log('🔍 [AUTH] Logging out user...');

      // Call logout endpoint to clear server-side session
      await fetch(`${AUTO_LOGIN_SERVICE_URL}/logout`, {
        method: 'POST',
        credentials: 'include',
      });

      console.log('✅ [AUTH] Server logout successful');
    } catch (error) {
      console.error('❌ [AUTH] Logout request failed:', error);
    } finally {
      clearAuthData();

      // Show logout notification
      notification.info({
        message: 'Logged Out',
        description: 'You have been successfully logged out.',
        duration: 3,
        placement: 'topRight'
      });
    }
  };

  const clearAuthData = () => {
    console.log('🔍 [AUTH] Clearing authentication data');
    setUser(null);
    setIsAuthenticated(false);
    setHasValidPackage(false);
    setAuthError(null);
    localStorage.removeItem('user');
    localStorage.removeItem('isAuthenticated');
  };

  const value = {
    user,
    isAuthenticated,
    loading,
    hasValidPackage,
    authError,
    login,
    logout,
    checkAuthStatus,
    checkPackageAccess: () => user ? checkPackageAccess(user.id) : Promise.resolve(),
  };

  return (
    <AuthContext.Provider value={value}>
      {children}
    </AuthContext.Provider>
  );
};
