/**
 * Login Component - Google OAuth Login
 */

import React, { useState, useEffect } from 'react';
import { Card, Button, Typography, Space, Alert, Spin } from 'antd';
import { GoogleOutlined, CheckCircleOutlined, ExclamationCircleOutlined } from '@ant-design/icons';
import { useAuth } from '../contexts/AuthContext';

const { Title, Text } = Typography;

// Auto-login service URL (should match your NestJS service)
const AUTO_LOGIN_SERVICE_URL = 'http://localhost:3000';

const Login = ({ onLoginSuccess }) => {
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [loginStatus, setLoginStatus] = useState(null);
  const { login } = useAuth();

  useEffect(() => {
    // Listen for messages from popup window
    const handleMessage = async (event) => {
      console.log('🔍 [LOGIN] Received message from popup:', event);
      console.log('🔍 [LOGIN] Event origin:', event.origin);
      console.log('🔍 [LOGIN] Expected origin:', AUTO_LOGIN_SERVICE_URL);

      if (event.origin !== AUTO_LOGIN_SERVICE_URL) {
        console.log('❌ [LOGIN] Origin mismatch, ignoring message');
        return;
      }

      console.log('🔍 [LOGIN] Message data:', event.data);

      if (event.data.type === 'AUTH_SUCCESS') {
        console.log('✅ [LOGIN] Received AUTH_SUCCESS, verifying token...');
        setLoading(true);
        setError(null);

        try {
          // Wait a bit for cookies to be set
          await new Promise(resolve => setTimeout(resolve, 500));

          console.log('🔍 [LOGIN] Making verify request to backend API');

          // Verify authentication with backend API (which proxies to auto-login service)
          const response = await fetch('/api/auth/verify-cookies', {
            method: 'GET',
            credentials: 'include', // Include cookies
          });

          console.log('🔍 [LOGIN] Verify response status:', response.status);
          console.log('🔍 [LOGIN] Verify response headers:', response.headers);

          if (response.ok) {
            const userData = await response.json();
            console.log('✅ [LOGIN] User data received:', userData);

            // Use AuthContext to handle login
            await login(userData.user);

            setLoginStatus({
              success: true,
              user: userData.user,
              message: 'Login successful!'
            });

            // Call the success callback if provided
            if (onLoginSuccess) {
              console.log('✅ [LOGIN] Calling onLoginSuccess callback');
              onLoginSuccess(userData.user);
            }

            // The ProtectedRoute will automatically redirect to dashboard
            // since the user is now authenticated via AuthContext
          } else {
            const errorText = await response.text();
            console.error('❌ [LOGIN] Verify failed:', response.status, errorText);
            throw new Error(`Failed to verify authentication: ${response.status} ${errorText}`);
          }
        } catch (err) {
          console.error('❌ [LOGIN] Authentication verification failed:', err);
          setError(`Authentication verification failed: ${err.message}. Please try again.`);
        } finally {
          setLoading(false);
        }
      } else if (event.data.type === 'AUTH_ERROR') {
        console.error('❌ [LOGIN] Received AUTH_ERROR:', event.data.message);
        setLoading(false);
        setError(event.data.message || 'Authentication failed');
      }
    };

    console.log('🔍 [LOGIN] Setting up message listener');
    window.addEventListener('message', handleMessage);

    return () => {
      console.log('🔍 [LOGIN] Removing message listener');
      window.removeEventListener('message', handleMessage);
    };
  }, [onLoginSuccess]);

  const handleGoogleLogin = async () => {
    console.log('🔍 [LOGIN] Starting Google login process');
    setLoading(true);
    setError(null);
    setLoginStatus(null);

    try {
      // Check if we're in Electron environment
      if (window.electronAPI) {
        console.log('🔍 [LOGIN] Detected Electron environment, using external browser');

        // Use Electron's shell to open external browser
        const authUrl = `${AUTO_LOGIN_SERVICE_URL}/auth/google`;
        console.log('🔍 [LOGIN] Opening external browser for OAuth:', authUrl);

        // Open in external browser
        await window.electronAPI.openExternal(authUrl);

        // Show user-friendly message
        setError('Please complete the login in your browser. We will automatically detect when you are logged in.');

        // Start polling for authentication status
        const pollInterval = setInterval(async () => {
          try {
            console.log('🔍 [LOGIN] Polling for auth status...');

            // Check for recent login first (faster than cookie verification)
            const recentLoginResponse = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/check-recent-login`, {
              method: 'GET',
              credentials: 'include',
            });

            if (recentLoginResponse.ok) {
              const loginData = await recentLoginResponse.json();
              console.log('✅ [LOGIN] Found recent login:', loginData);

              clearInterval(pollInterval);
              setError(null);

              // Set cookies manually for Electron app since external browser cookies aren't shared
              if (window.electronAPI && loginData.access_token) {
                console.log('🔍 [LOGIN] Setting cookies for Electron app...');

                // Set cookies using document.cookie (since we can't use httpOnly in renderer)
                const accessTokenExpiry = new Date(Date.now() + 3600000); // 1 hour
                const refreshTokenExpiry = new Date(Date.now() + 7 * 24 * 3600000); // 7 days

                document.cookie = `access_token=${loginData.access_token}; expires=${accessTokenExpiry.toUTCString()}; path=/; domain=localhost; SameSite=Lax`;
                document.cookie = `refresh_token=${loginData.refresh_token}; expires=${refreshTokenExpiry.toUTCString()}; path=/auth; domain=localhost; SameSite=Lax`;
                document.cookie = `electron_access_token=${loginData.access_token}; expires=${accessTokenExpiry.toUTCString()}; path=/; domain=localhost; SameSite=Lax`;

                console.log('✅ [LOGIN] Cookies set successfully for Electron app');
              }

              // Use AuthContext to handle login (skip package check since cookies aren't shared)
              await login(loginData.user, true);

              setLoginStatus({
                success: true,
                user: loginData.user,
                message: 'Login successful! Redirecting to dashboard...'
              });

              // The ProtectedRoute will automatically redirect to dashboard
              // since the user is now authenticated via AuthContext
              return;
            }

            // Fallback: check regular cookie-based auth
            const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/verify`, {
              method: 'GET',
              credentials: 'include',
            });

            if (response.ok) {
              const userData = await response.json();
              console.log('✅ [LOGIN] User authenticated via OAuth:', userData);

              clearInterval(pollInterval);
              setError(null);

              // Use AuthContext to handle login
              await login(userData.user);

              setLoginStatus({
                success: true,
                user: userData.user,
                message: 'Login successful! Redirecting to dashboard...'
              });

              // The ProtectedRoute will automatically redirect to dashboard
              // since the user is now authenticated via AuthContext
            }
          } catch (error) {
            console.log('🔍 [LOGIN] Still waiting for OAuth completion...');
          }
        }, 2000); // Poll every 2 seconds

        // Stop polling after 5 minutes
        setTimeout(() => {
          clearInterval(pollInterval);
          if (loading) {
            setLoading(false);
            setError('Login timeout. Please try again or contact support if the issue persists.');
          }
        }, 300000); // 5 minutes

      } else {
        console.log('🔍 [LOGIN] Web environment detected, using popup window');

        // Web version - use popup (fallback)
        const popupUrl = `${AUTO_LOGIN_SERVICE_URL}/auth/google`;
        console.log('🔍 [LOGIN] Opening popup to:', popupUrl);

        const popup = window.open(
          popupUrl,
          'google-login',
          'width=500,height=600,scrollbars=yes,resizable=yes'
        );

        // Check if popup was blocked
        if (!popup) {
          console.error('❌ [LOGIN] Popup was blocked');
          setLoading(false);
          setError('Popup was blocked. Please allow popups for this site and try again.');
          return;
        }

        console.log('✅ [LOGIN] Popup opened successfully');

        // Monitor popup and poll for authentication
        const checkClosed = setInterval(() => {
          if (popup.closed) {
            console.log('🔍 [LOGIN] Popup window closed');
            clearInterval(checkClosed);

            // Start polling for authentication after popup closes
            setTimeout(async () => {
              try {
                const response = await fetch(`${AUTO_LOGIN_SERVICE_URL}/auth/verify`, {
                  method: 'GET',
                  credentials: 'include',
                });

                if (response.ok) {
                  const userData = await response.json();
                  console.log('✅ [LOGIN] User authenticated after popup close:', userData);

                  // Use AuthContext to handle login
                  await login(userData);

                  setLoginStatus({
                    success: true,
                    user: userData,
                    message: 'Login successful! Redirecting to dashboard...'
                  });
                } else {
                  console.log('❌ [LOGIN] Authentication not completed');
                  setError('Login was cancelled or failed. Please try again.');
                }
              } catch (error) {
                console.error('❌ [LOGIN] Error checking auth status:', error);
                setError('Login verification failed. Please try again.');
              } finally {
                setLoading(false);
              }
            }, 1000);
          }
        }, 1000);
      }
    } catch (error) {
      console.error('❌ [LOGIN] Google login error:', error);
      setLoading(false);
      setError('Google login failed. Please try again.');
    }
  };

  if (loginStatus && loginStatus.success) {
    return (
      <div style={{
        display: 'flex',
        justifyContent: 'center',
        alignItems: 'center',
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
      }}>
        <Card
          style={{
            width: 400,
            textAlign: 'center',
            borderRadius: 16,
            boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
          }}
        >
          <Space direction="vertical" size="large" style={{ width: '100%' }}>
            <CheckCircleOutlined style={{ fontSize: 48, color: '#52c41a' }} />
            <Title level={3} style={{ margin: 0, color: '#52c41a' }}>
              Login Successful!
            </Title>
            <Text type="secondary">
              Welcome back, {loginStatus.user.email}
            </Text>
            <Spin size="small" />
            <Text type="secondary" style={{ fontSize: 12 }}>
              Redirecting to dashboard...
            </Text>
          </Space>
        </Card>
      </div>
    );
  }

  return (
    <div style={{ 
      display: 'flex', 
      justifyContent: 'center', 
      alignItems: 'center', 
      minHeight: '100vh',
      background: 'linear-gradient(135deg, #667eea 0%, #764ba2 100%)'
    }}>
      <Card 
        style={{ 
          width: 400, 
          textAlign: 'center',
          borderRadius: 16,
          boxShadow: '0 20px 40px rgba(0,0,0,0.1)'
        }}
      >
        <Space direction="vertical" size="large" style={{ width: '100%' }}>
          <div>
            <Title level={2} style={{ margin: 0, color: '#667eea' }}>
              Facebook Automation
            </Title>
            <Text type="secondary">
              Sign in to access your dashboard
            </Text>
          </div>

          {error && (
            <Alert
              message={error.includes('complete the login in your browser') ? 'Login in Progress' : 'Login Error'}
              description={error}
              type={error.includes('complete the login in your browser') ? 'info' : 'error'}
              icon={error.includes('complete the login in your browser') ? <CheckCircleOutlined /> : <ExclamationCircleOutlined />}
              showIcon
              closable
              onClose={() => setError(null)}
            />
          )}

          {loginStatus && loginStatus.success && (
            <Alert
              message="Login Successful"
              description={loginStatus.message}
              type="success"
              icon={<CheckCircleOutlined />}
              showIcon
            />
          )}

          <Button
            type="primary"
            size="large"
            icon={<GoogleOutlined />}
            onClick={handleGoogleLogin}
            loading={loading}
            disabled={loginStatus && loginStatus.success}
            style={{
              width: '100%',
              height: 48,
              fontSize: 16,
              background: loginStatus && loginStatus.success ? '#52c41a' : '#4285f4',
              borderColor: loginStatus && loginStatus.success ? '#52c41a' : '#4285f4',
              borderRadius: 8,
            }}
          >
            {loginStatus && loginStatus.success
              ? 'Login Successful!'
              : loading
                ? (error && error.includes('complete the login in your browser')
                    ? 'Waiting for browser login...'
                    : 'Signing in...')
                : 'Sign in with Google'
            }
          </Button>

          <div style={{ marginTop: 24 }}>
            <Text type="secondary" style={{ fontSize: 12 }}>
              By signing in, you agree to our Terms of Service and Privacy Policy.
              <br />
              You need an active account with bot Instagram package to access the features.
            </Text>
          </div>
        </Space>
      </Card>
    </div>
  );
};

export default Login;
